@extends('CLIENTES.plantilla')
@section('contenido')

    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
      {{--<img src="../img/logo.png" alt="Atras" style="width:120px;">--}}
        <a href="{{Route('home')}}"> <img src="../img/logo.png" alt="Logo" style="width:120px;"></a>
        <strong>{{$nombre_empresa}}</strong>
    </nav>
    </br>
    {{--*****************************************************--}}
                         {{--DEDICADAS--}}
    {{--*****************************************************--}}
        @php
        $tdedicadas = 0;
        @endphp

        @if (count($dedicadas) != '0')
                <div class="alert alert-success container">
                    <strong>Dedicadas</strong>
                </div>
                <table class="table table-sm">
                    <thead>
                       <tr>
                           <th width="30px">codigo</th>
                           <th width="500px">Descripcion</th>
                           <th width="30px">Precio</th>
                       </tr>
                    </thead>

                    <tbody>
                    @foreach($dedicadas as $dedicada)
                        <tr>
                            <td >{{$dedicada->codigo->codigo}}</td>
                            <td>{{$dedicada->codigo->descripcion}}</td>
                            <td> {{$dedicada->codigo->precio}}</td>
                         </tr>
                        @php
                            $tdedicadas = $tdedicadas + $dedicada->codigo->precio;
                        @endphp
                    @endforeach
                    <tr>
                        <th>Cantidad: {{count($dedicadas)}}</th>
                        <th></th>
                        <th> Precio Total: {{$tdedicadas}}</th>
                    </tr>
                    </tbody>
                </table>
        @endif
    
   {{--*****************************************************--}}
                         {{--PAQUETES--}}
    {{--*****************************************************--}}
        @php
        $tpaquetes = 0;
        @endphp

        @if (count($paquetes) != '0')
                <div class="alert alert-success container">
                    <strong>Paquetes</strong>
                </div>
                <table class="table table-sm">
                    <thead>
                       <tr>
                           <th width="30px">codigo</th>
                           <th width="500px">Descripcion</th>
                           <th width="30px">Precio</th>
                       </tr>
                    </thead>

                    <tbody>
                    @foreach($paquetes as $paquete)
                        <tr>
                            <td >{{$paquete->codigo->codigo}}</td>
                            <td>{{$paquete->codigo->descripcion}}</td>
                            <td> {{$paquete->codigo->precio}}</td>
                         </tr>
                        @php
                            $tpaquetes = $tpaquetes + $paquete->codigo->precio;
                        @endphp
                    @endforeach
                    <tr>
                        <th>Cantidad: {{count($paquetes)}}</th>
                        <th></th>
                        <th> Precio Total: {{$tpaquetes}}</th>
                    </tr>
                    </tbody>
                </table>
        @endif
    
   
    {{--*****************************************************--}}
    {{--SICEMASQL--}}
    {{--*****************************************************--}}

 @php
        $tsicemas = 0;
        @endphp

        @if (count($sicemas) != '0')
                <div class="alert alert-success container">
                    <strong>Sicema SQL</strong>
                </div>
                <table class="table table-sm">
                    <thead>
                       <tr>
                           <th width="30px">codigo</th>
                           <th width="500px">Descripcion</th>
                           <th width="30px">Precio</th>
                       </tr>
                    </thead>

                    <tbody>
                    @foreach($sicemas as $sicema)
                        <tr>
                            <td >{{$sicema->codigo->codigo}}</td>
                            <td>{{$sicema->codigo->descripcion}}</td>
                            <td> {{$sicema->codigo->precio}}</td>
                         </tr>
                        @php
                            $tsicemas = $tsicemas + $sicema->codigo->precio;
                        @endphp
                    @endforeach
                    <tr>
                        <th>Cantidad: {{count($sicemas)}}</th>
                        <th></th>
                        <th> Precio Total: {{$tsicemas}}</th>
                    </tr>
                    </tbody>
                </table>
        @endif
    {{--*****************************************************--}}
    {{--DATADIN--}}
    {{--*****************************************************--}}

 @php
        $tdatadins = 0;
        @endphp

        @if (count($datadins) != '0')
                <div class="alert alert-success container">
                    <strong>DataDin</strong>
                </div>
                <table class="table table-sm">
                    <thead>
                       <tr>
                           <th width="30px">codigo</th>
                           <th width="500px">Descripcion</th>
                           <th width="30px">Precio</th>
                       </tr>
                    </thead>

                    <tbody>
                    @foreach($datadins as $datadin)
                        <tr>
                            <td >{{$datadin->codigo->codigo}}</td>
                            <td>{{$datadin->codigo->descripcion}}</td>
                            <td> {{$datadin->codigo->precio}}</td>
                         </tr>
                        @php
                            $tdatadins = $tdatadins + $datadin->codigo->precio;
                        @endphp
                    @endforeach
                    <tr>
                        <th>Cantidad: {{count($datadins)}}</th>
                        <th></th>
                        <th> Precio Total: {{$tdatadins}}</th>
                    </tr>
                    </tbody>
                </table>
        @endif
    
{{--********************************************************************--}}
                    {{--TOTALES--}}
{{--********************************************************************--}}
    @php
        $totales=0;
        $totales=$tdedicadas + $tpaquetes + $tsicemas + $tdatadins;
    @endphp
    <div class="alert alert-warning container">
        <strong>TOTALES</strong>
        <strong class="float-right">${{$totales}}</strong>
    </div>

@endsection
