@extends('CLIENTES.plantilla')
@section('contenido')
    <?php
    $ap=array();
    $no=array();
    $ape=array();
    ?>
  @foreach ($trans as $tran)
  <?php
  $ap[$tran->no_movil]= $tran->no_movil;
  $no[$tran->nombre]=$tran->nombre;
  $ape[$tran->apellidos]=$tran->apellidos;
  ?>
  @endforeach
    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
    </nav>
	</br>
 {{Form::open(['Route'=> 'apns.index', 'method'=>'GET', 'class'=>'form-inline pull-right'])}}
    <div class="form-group">
        {!!Form::select('ueb', array('Pinar del rio'=>'Pinar del Rio','Habana'=>'Habana','Villa Clara'=>'Villa Clara','Camaguey'=>'Camaguey','Oriente'=>'Oriente'), null, ['class'=>'form-control','placeholder'=>'Todos'])!!}
        {!! Form::button('<i class="fa fa-user-plus"></i>'.' Aceptar', ['type'=>'submit','class'=> 'btn btn-primary'] ) !!}
    </div>
    {{Form::close()}}
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> APNS ({{$trans->total()}}) </strong>
    </div>
    <table class="table table-hover table-striped table-sm">
        <thead>
        <tr>
            <th style="width:30px;">Establecimiento</th>
            <th style="width:30px;">Empresa</th>
            <th style="width:30px;">Movil</th>
            <th style="width:20px;">Nombre</th>
            <th style="width:20px;">Cargo</th>
            <th style="width:20px;">IP</th>
            <th style="width:20px;">Acceso</th>
            <th style="width:20px;">Estado</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                <td>{{$tran->establecimiento}}</td>
                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->no_movil}}</td>
                <td>{{$tran->nombre}}</td>
                <td>{{$tran->cargo}}</td>
                <td>{{$tran->ip}}</td>
                <td>{{$tran->acceso}}</td>
                <td>{{$tran->estado}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    {!! $trans->render() !!}
    {{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
  <script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection
