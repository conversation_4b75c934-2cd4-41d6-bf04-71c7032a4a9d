<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Dedicada extends Model
{
    public function codigo(){
        return $this->belongsTo(Codigo::class);
    }
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeIna($query, $ina)
    {
        if ($ina != ""){
            return $query->where('ina','LIKE', "%$ina%");
        }
    }

    public function scopeLan($query, $lan)
    {
        if ($lan != ""){
            return $query->where('lan','LIKE', "%$lan%");
        }
    }

    public function scopeLc($query, $lc)
    {
        if ($lc != ""){
            return $query->where('lc','LIKE', "%$lc%");
        }
    }

    public function scopevelocidad($query, $velocidad)
    {
        if ($velocidad != ""){
            return $query->where('velocidad','LIKE', "%$velocidad%");
        }
    }
}
