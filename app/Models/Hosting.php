<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Hosting extends Model
{
    public function codigo(){
        return $this->belongsTo(Codigo::class);
    }
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeNombre($query, $nombre)
    {
        if ($nombre != ""){
            return $query->where('nombre_sitio','LIKE', "%$nombre%");
        }
    }
}
