<?php

namespace App\Models\Scopes;

use App\Models\Cliente;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UebScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->whereHas('cliente', function (Builder $builder) {
            $builder->where('uebalimatic', 'LIKE', '%' . auth()->user()->UEB . '%');
        });
    }
}
