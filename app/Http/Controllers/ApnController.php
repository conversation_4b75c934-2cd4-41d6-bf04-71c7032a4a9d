<?php

namespace App\Http\Controllers;

use App\Models\Apn;
use App\Models\Dedicada;
use App\Models\Empresa;
use App\Models\Paquete;
use Illuminate\Http\Request;
use App\Http\Requests\ApnRequest;
use App\Http\Requests\UpdateRequest;

class ApnController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $nombre = $request->get('nombre');
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $movil = $request->get('no_movil');
        $trans = Apn::query()->orderby('no_movil','ASC')
            ->movil($movil)
            ->nombre($nombre)
            ->whereHas('cliente', function ($query) use ($request) {
                $cliente = $request->get('ueb');
                $query->where('uebalimatic', 'LIKE', '%' . $cliente . '%');
            })
            ->paginate(100);
        $totalApn = $trans->total();
        $empre= Empresa::all();

        return view('CLIENTES.APN.index',compact('trans','empre'));
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
     return('hola');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ApnRequest $request)
    {
        if ($request->acceso == 'Alinet'){
    $trans=new Apn;
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->no_movil = $request->no_movil;
        $trans->nombre = $request->nombre;
        $trans->cargo = $request->cargo;
        $trans->ip = $request->ip;
        $trans->acceso = $request->acceso;
        $trans->estado = $request->estado;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'APN Insertada correctamente');
        }
        else{
            // Buscar cantidad de APN Autorizadas
                   $temp=Paquete::orderby('id','DESC')
                   ->where('cliente_id',$request->cliente_id)->get();
                   $cant=0;
                   $cantapn=0;
                    foreach ($temp as $temp) {
                          $cant = $cant + $temp->apn;
                    }
        // *************************************
                   $temp2=Apn::orderby('id','DESC')
                         ->where('cliente_id',$request->cliente_id)->get();
                    $cantApn = count($temp2);
        //if ($cantApn <= 4){
        //$trans=new apn;
        //$trans->cliente_id = $request->cliente_id;
        //$trans->establecimiento = $request->establecimiento;
        //$trans->no_movil = $request->no_movil;
        //$trans->nombre = $request->nombre;
        //$trans->cargo = $request->cargo;
        //$trans->ip = $request->ip;
        //$trans->save();
        //$cliente=$trans->cliente_id;
        //return redirect()->route('clientes.show',[$cliente])->with('info', 'APN Insertada correctamente');
        //}
        if ($cant <= $cantApn){
          return back()->with('infod','Actualice o Contrate otro paquete de servicios de ALINET, el actual llego al tope');
        }
        else{
        // Salvar APN
    $trans=new Apn;
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->no_movil = $request->no_movil;
        $trans->nombre = $request->nombre;
        $trans->cargo = $request->cargo;
        $trans->ip = $request->ip;
        $trans->acceso = $request->acceso;
        $trans->estado = $request->estado;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'APN Insertada correctamente');
        }
        }



    }
    public function show(Apn $apn)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\apn  $apn
     * @return \Illuminate\Http\Response
     */
    public function edit(Apn $apn)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\apn  $apn
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, $id)
    {
         if ($request->acceso == 'Alinet'){
             $trans=Apn::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->no_movil = $request->no_movil;
        $trans->nombre = $request->nombre;
        $trans->cargo = $request->cargo;
        $trans->ip = $request->ip;
        $trans->acceso = $request->acceso;
        $trans->estado = $request->estado;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'APN Actualizada correctamente');
         }
        else {
          $temp=Paquete::orderby('id','DESC')
                   ->where('cliente_id',$request->cliente_id)->get();
                   $cant=0;
                   $cantapn=0;
                    foreach ($temp as $temp) {
                          $cant = $cant + $temp->apn;
                    }
        // *************************************
                   $temp2=Apn::orderby('id','DESC')
                         ->where('cliente_id',$request->cliente_id)->get();
                    $cantApn = count($temp2);
          if ($cant <= $cantApn){
          return back()->with('infod','Actualice o Contrate otro paquete de servicios de ALINET, el actual llego al tope');
        }
        else{
            $trans=Apn::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->no_movil = $request->no_movil;
        $trans->nombre = $request->nombre;
        $trans->cargo = $request->cargo;
        $trans->ip = $request->ip;
        $trans->acceso = $request->acceso;
        $trans->estado = $request->estado;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'APN Actualizada correctamente');
        }
        }



    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\apn  $apn
     * @return \Illuminate\Http\Response
     */
    public function destroy($apn)
    {
    $trans = Apn::find($apn);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'APN eliminada correctamente');
    }
}
